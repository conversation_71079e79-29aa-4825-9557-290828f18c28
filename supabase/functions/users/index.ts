import { Hono } from "@hono/hono";
import type { User } from "@supabase/supabase-js";
import { zValidator } from "npm:@hono/zod-validator";
import { bearerAuth } from "npm:hono@^4.0.0/bearer-auth";
import { cors } from "npm:hono@^4.0.0/cors";
import { z } from "npm:zod";
import { hasPermission, permissions } from "../_shared/permissions.ts";
import { supabaseAdmin } from "../_shared/supabaseAdmin.ts";
import { createUser, deleteUser, getUser, resendInvitation } from "./utils.ts";

const functionName = "users";
const app = new Hono().basePath(`/${functionName}`);

declare module "@hono/hono" {
  interface ContextVariableMap {
    user: User;
  }
}

app.use(
  "*",
  cors({
    origin: [
      "http://localhost:5173",
      "https://*.bizzu.app",
      "https://*.bizzu.dev",
    ],
  })
);

app.use(
  "*",
  bearerAuth({
    verifyToken: async (token, c) => {
      const { data, error } = await supabaseAdmin.auth.getUser(token);

      if (error || !data) {
        return false;
      }

      c.set("user", data.user);

      return true;
    },
  })
);

app.post(
  "/create",
  zValidator(
    "json",
    z.object({
      email: z.email(),
      name: z.string(),
      lastName: z.string(),
      roleId: z.string(),
    })
  ),
  async (c) => {
    const authUser = c.get("user");
    const { email, name, lastName, roleId } = c.req.valid("json");

    const canCreateUsers = await hasPermission(
      permissions.users.create,
      authUser.id
    );

    if (!canCreateUsers) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { data, error } = await createUser({
      email,
      name,
      lastName,
      roleId,
      workgroupId: authUser.app_metadata.workgroup_id,
    });

    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ id: data.user.id });
  }
);

app.post(
  "/resend-invitation",
  zValidator(
    "json",
    z.object({
      email: z.email(),
    })
  ),
  async (c) => {
    const authUser = c.get("user");
    const { email } = c.req.valid("json");

    const canCreateUsers = await hasPermission(
      permissions.users.create,
      authUser.id
    );

    if (!canCreateUsers) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { error } = await resendInvitation(email);

    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ ok: true });
  }
);

app.delete("/:id", async (c) => {
  const authUser = c.get("user");
  const { id } = c.req.param();

  const canDeleteUsers = await hasPermission(
    permissions.users.delete,
    authUser.id
  );

  if (!canDeleteUsers) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  const [{ error: postgresError }, { error: authError }] = await deleteUser(id);

  if (postgresError || authError) {
    return c.json({ error: postgresError?.message || authError?.message }, 500);
  }

  return c.json({ ok: true });
});

app.get("/:id/confirmation-status", async (c) => {
  const authUser = c.get("user");
  const { id } = c.req.param();

  const canUpdateUsers = await hasPermission(
    permissions.users.update,
    authUser.id
  );

  if (!canUpdateUsers) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  const { error, data } = await getUser(id);

  if (error) {
    return c.json({ error: error.message }, 500);
  }

  if (data.user.email_confirmed_at) {
    return c.json({ confirmed: true });
  }

  return c.json({ confirmed: false });
});

Deno.serve(app.fetch);
