import supabase from "@/utils/supabase";
import type { CreateUserPayload, UpdateUserPayload, User } from "./types";

export async function getUsers(): Promise<User[]> {
    const { data, error } = await supabase.from("user_profiles").select("*").is("deleted_at", null);

    if (error) {
        throw error;
    }

    return data as User[];
}

export async function getUser(userId: string): Promise<User | null> {
    const { data, error } = await supabase.from("user_profiles").select("*").eq("id", userId);

    if (error) {
        throw error;
    }

    return data[0] as User | null;
}

export async function updateUser(userId: string, user: UpdateUserPayload): Promise<void> {
    const { error } = await supabase
        .from("user_profiles")
        .update({
            first_name: user.first_name,
            last_name: user.last_name,
        })
        .eq("id", userId);

    if (error) {
        throw error;
    }
}

export async function createUser(user: CreateUserPayload): Promise<void> {
    const { error } = await supabase.functions.invoke("users/create", {
        method: "POST",
        body: user,
    });

    if (error) {
        throw error;
    }
}

export async function resendInvitation(email: string): Promise<void> {
    const { error } = await supabase.functions.invoke("users/resend-invitation", {
        method: "POST",
        body: { email },
    });

    if (error) {
        throw error;
    }
}

export async function deleteUser(userId: string): Promise<void> {
    const { error } = await supabase.functions.invoke(`users/${userId}`, {
        method: "DELETE",
    });

    if (error) {
        throw error;
    }
}

export async function getUserConfirmationStatus(userId: string): Promise<boolean> {
    const { data, error } = await supabase.functions.invoke(`users/${userId}/confirmation-status`, {
        method: "GET",
    });

    if (error) {
        throw error;
    }

    return data.confirmed as boolean;
}

export async function resetPassword(email: string): Promise<void> {
    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
        throw error;
    }

    return Promise.resolve();
}
