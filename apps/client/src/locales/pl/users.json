{"addWorker": "<PERSON><PERSON><PERSON>", "moduleSubtitle": "Dodawaj <PERSON>ych, edytuj istniejących wchodząc w ich szczegóły.", "firstName": "<PERSON><PERSON><PERSON>", "firstNamePlaceholder": "<PERSON><PERSON><PERSON>", "lastName": "Nazwisko", "lastNamePlaceholder": "Nazwisko pracownika", "email": "Email", "emailPlaceholder": "<PERSON><PERSON>", "role": "Rola", "role_admin": "Administrator", "primaryDataSection": "<PERSON> p<PERSON>", "userActionSection": "<PERSON><PERSON><PERSON><PERSON>", "deleteUser": "Usuń pracownika", "resendInvitation": "Wyślij ponownie zaproszenie", "resendInvitationNotification": "Wysłano ponowne zaproszenia na adres \"{{email}}\"", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "changePassword": "<PERSON><PERSON><PERSON> hasło", "userUpdated": "Pracownik zaktualizowany", "userUpdateFailed": "Błąd podczas aktualizacji pracownika", "userCreated": "Pracownik utworzony", "userCreateFailed": "Błąd podczas tworzenia pracownika", "invitationSent": "Zaproszenie wysłane", "invitationSentFailed": "Błąd podczas wysyłania zaproszenia", "deletedUser": "Pracownik {{name}} usunięty", "deletedUserFailed": "Błąd podczas usuwania pracownika", "deleteModal": {"title": "Usuń pracownika", "description": "Usunięcie pracownika skutkować będzie:", "deletionResult1": "odebraniem mu możliwości logowania się do systemu", "deletionResult2": "usunięciem go z listy pracowników", "descriptionNotice": "<PERSON><PERSON> to, jego dane będą wciąż widoczne w systemie w miejscach, gdzie jest on wspomniany.", "warning": "Operacja jest nieodwracalna.", "actionButtonText": "Usuń", "cancelButtonText": "<PERSON><PERSON><PERSON>"}, "resetPasswordModal": {"title": "<PERSON><PERSON><PERSON> hasła", "description": "W celu zmiany hasła wyślemy Ci na maila link, po wejściu na który, b<PERSON><PERSON><PERSON><PERSON> mieć możliwość ustawienia hasła.", "actionButtonText": "Wyślij link do zmiany hasła", "cancelButtonText": "<PERSON><PERSON><PERSON>", "notification": "Link do zmiany hasła został wysłany. Za chwilę nastąpi wylogowanie", "notificationFailed": "Błąd podczas wysyłania linku do zmiany hasła"}, "passwordResetPage": {"title": "Sprawdź swoją pocztę e-mail", "description": "W celu zmiany hasła wysłaliśmy Ci link, po wejściu na który, bę<PERSON>ziesz mógł zmienić swoje hasło.", "backToLogin": "Powrót do logowania"}}