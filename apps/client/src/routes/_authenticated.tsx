import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { getUserPermissionsQueryConfig, getUserQueryConfig } from "@/api/auth";
import { AppLayout } from "@/components/layouts/appLayout";

export const Route = createFileRoute("/_authenticated")({
    beforeLoad: async ({ context, location }) => {
        const { queryClient } = context;
        try {
            await queryClient.fetchQuery(getUserQueryConfig());
            await queryClient.fetchQuery(getUserPermissionsQueryConfig());
        } catch (e) {
            if (e instanceof Error && e.message.includes("401")) {
                throw redirect({ to: "/login", search: { redirect: location.pathname } });
            }
        }
    },
    component: () => (
        <AppLayout>
            <Outlet />
        </AppLayout>
    ),
});
