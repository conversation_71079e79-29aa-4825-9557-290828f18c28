import { registerDomain } from "../../api/registerDomain";
import styles from "./registerDomain.module.scss";
import { useState } from "react";
import {
  getInvitationCode,
  getNavigationLink,
  setAuthCookie,
  validateSubdomain,
} from "./utils";
import { PrimaryButton } from "../../components/button/button";

export default function RegisterDomain() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [subdomain, setSubdomain] = useState("");
  const [formMessage, setFormMessage] = useState<{
    text: string;
    type: "error" | "success";
  } | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormMessage(null);
    setIsLoading(true);

    try {
      if (!validateSubdomain(subdomain)) {
        setFormMessage({
          text: "Nazwa konta jest niepoprawna. Nazwa może składać się z małych liter, cyfr i myślników.",
          type: "error",
        });
        return;
      }

      const result = await registerDomain({
        password,
        email,
        subdomain,
        invitationCode: getInvitationCode() || "",
        companyName,
      });

      if (result.requiresEmailConfirmation) {
        // Redirect to email confirmation page
        window.location.href = `/registration-email-confirmation?email=${encodeURIComponent(result.email)}`;
        return;
      }

      setFormMessage({
        text: "Konto zostało utworzone. Za chwilę zostaniesz przekierowany do Twojej aplikacji",
        type: "success",
      });

      setAuthCookie(result.accessToken!);

      setTimeout(() => {
        window.location.href = getNavigationLink(subdomain);
      }, 600);
    } catch (e) {
      setFormMessage({
        text: (e as Error).message,
        type: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageContent}>
        <h2>Rejestracja konta dla Twojej firmy</h2>
        <p>
          Wypełnij poniższe dane, aby utworzyć konto administratora i
          skonfigurować Twoją firmę w Bizzu.app.
        </p>
        <form onSubmit={handleSubmit}>
          <div className={styles.formSections}>
            <div className={styles.formSection}>
              <h3 className={styles.sectionTitle}>Dane użytkownika</h3>
              <p className={styles.sectionDescription}>
                Te dane posłużą do zalogowania do systemu jako administrator
              </p>
              <div>
                <label htmlFor="email">Email / Login</label>
                <input
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  type="email"
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label htmlFor="password">Hasło</label>
                <input
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  type="password"
                  name="password"
                  id="password"
                  placeholder="Minimum 6 znaków"
                />
              </div>
            </div>

            <div className={styles.formSection}>
              <h3 className={styles.sectionTitle}>Dane firmy</h3>
              <p className={styles.sectionDescription}>
                Skonfiguruj nazwę firmy i adres Twojej aplikacji
              </p>
              <div>
                <label htmlFor="companyName">Nazwa firmy</label>
                <input
                  onChange={(e) => setCompanyName(e.target.value)}
                  required
                  type="text"
                  name="companyName"
                  id="companyName"
                  placeholder="Nazwa Twojej firmy"
                />
              </div>
              <div>
                <label htmlFor="subdomain">Nazwa konta / Subdomena</label>
                <input
                  onChange={(e) => setSubdomain(e.target.value)}
                  required
                  type="text"
                  name="subdomain"
                  id="subdomain"
                  placeholder="nazwa-firmy"
                />
                <div className={styles.subdomainHint}>
                  Twoja aplikacja będzie dostępna pod adresem:{" "}
                  <div>
                    <strong>{subdomain || "nazwa-konta"}.bizzu.app</strong>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {formMessage && (
            <div
              className={`${styles.formMessage} ${styles[formMessage.type]}`}
            >
              {formMessage.text}
            </div>
          )}
          {isLoading && (
            <div className={styles.loadingMessage}>Wysyłanie...</div>
          )}
          <PrimaryButton
            disabled={isLoading}
            type="submit"
            className={styles.button}
          >
            Utwórz konto
          </PrimaryButton>
        </form>
      </div>
    </div>
  );
}
