import { createFileRoute, Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { AuthLayout } from "@/components/layouts/authLayout";

export const Route = createFileRoute("/_public/check-email")({
    component: RouteComponent,
});

function RouteComponent() {
    const { t } = useTranslation("users");

    return (
        <AuthLayout title={t("passwordResetPage.title")} subtitle={""}>
            <div className="flex flex-col items-center justify-center gap-4 p-4">
                <p className="text-center">{t("passwordResetPage.description")}</p>
                <RouterLink to="/login" className="text-primary text-sm hover:underline">
                    {t("passwordResetPage.backToLogin")}
                </RouterLink>
            </div>
        </AuthLayout>
    );
}
