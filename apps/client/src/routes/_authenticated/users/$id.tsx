import type { User } from "@supabase/supabase-js";
import { createFileRoute, redirect, useLoaderData, useRouter } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import type { Permission } from "@/api/auth";
import {
    getUserQueryConfig as getCurrentUserQueryConfig,
    getUserPermissionsQueryConfig,
    useSignOut,
} from "@/api/auth/hooks";
import { rolesQueryConfig } from "@/api/roles/hooks";
import {
    getUserConfirmationStatusQueryConfig,
    getUserQueryConfig,
    useDeleteUser,
    useResendInvitation,
    useResetPassword,
    useUpdateUser,
} from "@/api/users/hooks";
import { Breadcrumbs } from "@/components/breadcrumbs/breadcrumbs";
import { ModuleLayout } from "@/components/layouts/moduleLayout";
import { UserActions, UserForm } from "@/features/userForm";
import type { UserForm as UserFormType } from "@/features/userForm/types";
import { createBreadcrumbConfig, createBreadcrumbItem } from "@/utils/breadcrumbs";
import { canViewUserProfile } from "@/utils/permissions";

export const Route = createFileRoute("/_authenticated/users/$id")({
    component: RouteComponent,
    loader: async ({ params, context }) => {
        const { queryClient } = context;
        const user = await queryClient.fetchQuery(getUserQueryConfig(params.id));
        const currentUser = (await queryClient.getQueryData(getCurrentUserQueryConfig().queryKey)) as User;
        const permissions = queryClient.getQueryData<Permission[]>(getUserPermissionsQueryConfig().queryKey) ?? [];

        if (!user || !canViewUserProfile(permissions, user.id, currentUser.id)) {
            throw redirect({ to: "/users" });
        }

        await queryClient.fetchQuery(getUserConfirmationStatusQueryConfig(params.id));
        await queryClient.fetchQuery(rolesQueryConfig);

        return { user };
    },
});

function RouteComponent() {
    const { user } = useLoaderData({ from: "/_authenticated/users/$id" });
    const { t } = useTranslation(["users"]);
    const breadcrumbs = createBreadcrumbConfig([
        createBreadcrumbItem("Pracownicy", "/users"),
        createBreadcrumbItem(`${user.first_name} ${user.last_name}`, "/users/$id"),
    ]);
    const router = useRouter();
    const { mutate: updateUser } = useUpdateUser(user.id);
    const { mutate: resentInvitation } = useResendInvitation();
    const { mutate: deleteUser } = useDeleteUser();
    const { mutate: resetPassword } = useResetPassword();
    const { mutate: signOut } = useSignOut();

    const handleSubmit = (data: UserFormType) => {
        updateUser(
            {
                first_name: data.firstName,
                last_name: data.lastName,
                role_id: data.role_id,
            },
            {
                onSuccess: () => {
                    toast.success(t("userUpdated"));
                },
                onError: () => {
                    toast.error(t("userUpdateFailed"));
                },
            },
        );
    };

    const handleResendInvitation = () => {
        resentInvitation(user.email, {
            onSuccess: () => {
                toast.success(t("invitationSent"));
            },
            onError: () => {
                toast.error(t("invitationSentFailed"));
            },
        });
    };
    const handleDeleteUser = () => {
        deleteUser(user.id, {
            onSuccess: () => {
                toast.success(t("deletedUser", { name: `${user.first_name} ${user.last_name}` }));
                router.navigate({ to: "/users" });
            },
            onError: () => {
                toast.error(t("deletedUserFailed"));
            },
        });
    };

    const handleResetPassword = () => {
        resetPassword(user.email, {
            onSuccess: () => {
                toast.success(t("resetPasswordModal.notification"));

                setTimeout(() => {
                    signOut(void 0, {
                        onSettled: () => {
                            router.navigate({ to: "/check-email" });
                        },
                    });
                }, 1000);
            },
            onError: () => {
                toast.error(t("resetPasswordModal.notificationFailed"));
            },
        });
    };

    return (
        <ModuleLayout>
            <ModuleLayout.Header>
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            </ModuleLayout.Header>
            <ModuleLayout.Content>
                <div className="grid gap-4 lg:grid-cols-3">
                    <UserForm editMode="inline" user={user} onSubmit={handleSubmit} />
                    <UserActions
                        onResendInvitation={handleResendInvitation}
                        onDeleteUser={handleDeleteUser}
                        onResetPassword={handleResetPassword}
                        userId={user.id}
                    />
                </div>
            </ModuleLayout.Content>
        </ModuleLayout>
    );
}
